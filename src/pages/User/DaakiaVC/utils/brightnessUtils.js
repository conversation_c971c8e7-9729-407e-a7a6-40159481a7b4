// Brightness manager utility - stateless functions like virtualBackground.js
import { RoomEvent } from "livekit-client";

// Debug flag - set to true to see console logs, false to hide them
const DEBUG_BRIGHTNESS = true;

let participantBrightness = new Map();
let rpcRegistered = false;
let throttleTimer = null;
let lastThrottleTime = 0;
let currentRoom = null;
let participantsSentBrightness = new Set(); // Track which participants have received current brightness
let lastSentBrightness = 100; // Track the last brightness value sent

let handleParticipantDisconnected = null;
let handleRoomDisconnected = null;
let handleConnectionStateChanged = null;

const handleRpcMessage = (data) => {
  try {
    const payload = JSON.parse(data.payload);
    const { brightness } = payload;
    participantBrightness.set(data.callerIdentity, brightness);
    return "Brightness updated successfully";
  } catch (error) {
    console.error('Brightness RPC processing failed:', error.message);
    return "Error: Failed to update brightness";
  }
};

// Initialize brightness RPC for a room (like toggleBlur function)
export async function initializeBrightnessRPC(room) {
  if (!room || room.state !== 'connected') {
    return;
  }

  // Prevent double registration
  if (currentRoom === room && rpcRegistered) {
    return;
  }

  try {
    // IMPORTANT: Always unregister first to prevent "overriding RPC handler" error
    // This is required by LiveKit - must call unregisterRpcMethod before registerRpcMethod
    try {
      room.localParticipant.unregisterRpcMethod('setBrightness');
    } catch (error) {
      // This is expected if no method was registered before
    }

    // Now register the RPC method
    room.localParticipant.registerRpcMethod('setBrightness', handleRpcMessage);

    rpcRegistered = true;
    currentRoom = room;

    // Setup participant event listeners - store references for cleanup
    handleParticipantDisconnected = (participant) => {
      participantBrightness.delete(participant.identity);
      participantsSentBrightness.delete(participant.identity);
      if (DEBUG_BRIGHTNESS) console.log(`Participant ${participant.identity} disconnected, removed from brightness tracking`);
    };

    handleRoomDisconnected = () => {
      participantBrightness.clear();
      participantsSentBrightness.clear();
      lastSentBrightness = 100;
      rpcRegistered = false;
      currentRoom = null;
      if (DEBUG_BRIGHTNESS) console.log('Room disconnected, cleared all brightness tracking');
    };

    handleConnectionStateChanged = (state) => {
      // Reset RPC registration if connection is lost
      if (state !== 'connected') {
        rpcRegistered = false;
      }
    };

    room.on(RoomEvent.ParticipantDisconnected, handleParticipantDisconnected);
    room.on(RoomEvent.Disconnected, handleRoomDisconnected);
    room.on(RoomEvent.ConnectionStateChanged, handleConnectionStateChanged);

  } catch (error) {
    console.error('Failed to register brightness RPC method:', error.message);
    rpcRegistered = false;
  }
}

// Separate function for the actual send operation
async function sendBrightnessImmediate(room, brightness) {
  try {
    const remoteParticipants = Array.from(room.remoteParticipants.values());

    if (remoteParticipants.length === 0) {
      if (DEBUG_BRIGHTNESS) console.log('sendBrightnessImmediate: No remote participants to send brightness to');
      return;
    }

    // If brightness value changed, reset the tracking set
    if (brightness !== lastSentBrightness) {
      participantsSentBrightness.clear();
      lastSentBrightness = brightness;
      if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessImmediate: Brightness changed to ${brightness}, cleared tracking set`);
    }

    // Filter out participants who have already received this brightness value
    const participantsToSend = remoteParticipants.filter(participant =>
      !participantsSentBrightness.has(participant.identity)
    );

    if (participantsToSend.length === 0) {
      if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessImmediate: All ${remoteParticipants.length} participants already have brightness ${brightness}, skipping send`);
      return;
    }

    if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessImmediate: Sending brightness ${brightness} to ${participantsToSend.length} of ${remoteParticipants.length} participants (${remoteParticipants.length - participantsToSend.length} already have it)`);

    // Send to participants who haven't received this brightness yet
    const sendPromises = participantsToSend.map(async (participant) => {
      try {
        if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessImmediate: Sending brightness ${brightness} to participant ${participant.identity}`);
        const response = await room.localParticipant.performRpc({
          destinationIdentity: participant.identity,
          method: 'setBrightness',
          payload: JSON.stringify({ brightness }),
          responseTimeout: 5000,
        });

        // Mark this participant as having received the brightness
        participantsSentBrightness.add(participant.identity);
        if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessImmediate: Successfully sent brightness to ${participant.identity}, response:`, response);
        return { participant: participant.identity, success: true, response };
      } catch (error) {
        console.error(`Failed to send brightness to ${participant.identity}:`, error.message);
        return { participant: participant.identity, success: false, error: error.message };
      }
    });

    const results = await Promise.all(sendPromises);
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessImmediate: All brightness send operations completed:', results);
  } catch (error) {
    console.error('Brightness send operation failed:', error.message);
  }
}

// Send brightness to all participants (like toggleBlur function)
export async function sendBrightnessToAll(room, brightness) {
  if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToAll: Called with brightness ${brightness}, room state: ${room?.state}`);

  if (!room || room.state !== 'connected') {
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Room not connected, skipping');
    return;
  }

  if (brightness === 100) {
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Brightness is 100 (default), skipping send');
    return;
  }

  // Throttle the send operation for better UI responsiveness
  const now = Date.now();
  const throttleDelay = 150; // 150ms throttle for smooth UI updates

  if (now - lastThrottleTime < throttleDelay) {
    if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToAll: Throttling - scheduling delayed send in ${throttleDelay - (now - lastThrottleTime)}ms`);
    // Clear existing timer and set new one
    if (throttleTimer) {
      clearTimeout(throttleTimer);
    }

    throttleTimer = setTimeout(() => {
      lastThrottleTime = Date.now();
      if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Executing throttled send');
      sendBrightnessImmediate(room, brightness);
    }, throttleDelay - (now - lastThrottleTime));
    return;
  }

  if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToAll: Sending immediately (no throttle needed)');
  lastThrottleTime = now;
  sendBrightnessImmediate(room, brightness);
}

export async function sendBrightnessToNewParticipant(room, participant, brightness) {
  if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToNewParticipant: Called for ${participant?.identity} with brightness ${brightness}`);

  if (!room || room.state !== 'connected') {
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToNewParticipant: Room not connected, skipping');
    return;
  }

  if (!participant || !participant.identity) {
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToNewParticipant: Invalid participant, skipping');
    return;
  }

  if (brightness === 100) {
    if (DEBUG_BRIGHTNESS) console.log('sendBrightnessToNewParticipant: Brightness is 100 (default), skipping');
    return; // No need to send default brightness
  }

  // Check if this participant already has the current brightness
  if (brightness === lastSentBrightness && participantsSentBrightness.has(participant.identity)) {
    if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToNewParticipant: Participant ${participant.identity} already has brightness ${brightness}, skipping`);
    return;
  }

  // Wait a bit for participant to be ready
  setTimeout(async () => {
    try {
      if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToNewParticipant: Sending brightness ${brightness} to new participant ${participant.identity}`);
      await room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: 'setBrightness',
        payload: JSON.stringify({ brightness }),
        responseTimeout: 5000,
      });

      // Mark this participant as having received the brightness
      participantsSentBrightness.add(participant.identity);
      if (DEBUG_BRIGHTNESS) console.log(`sendBrightnessToNewParticipant: Successfully sent brightness to new participant ${participant.identity}`);
    } catch (error) {
      console.error(`Failed to send brightness to new participant ${participant.identity}:`, error.message);
    }
  }, 3000);
}

// Get all participant brightness values (simple getter)
export function getAllParticipantBrightness() {
  return new Map(participantBrightness);
}

// Cleanup brightness RPC (like noEffect function)
export function cleanupBrightnessRPC() {
  if (currentRoom && rpcRegistered) {
    try {
      currentRoom.localParticipant.unregisterRpcMethod('setBrightness');
    } catch (error) {
      console.error('Failed to unregister brightness RPC method:', error.message);
    }
    try {
      if (handleParticipantDisconnected) {
        currentRoom.off(RoomEvent.ParticipantDisconnected, handleParticipantDisconnected);
      }
      if (handleRoomDisconnected) {
        currentRoom.off(RoomEvent.Disconnected, handleRoomDisconnected);
      }
      if (handleConnectionStateChanged) {
        currentRoom.off(RoomEvent.ConnectionStateChanged, handleConnectionStateChanged);
      }
    } catch (error) {
      console.error('Failed to remove brightness event listeners:', error.message);
    }
  }

  if (throttleTimer) {
    clearTimeout(throttleTimer);
    throttleTimer = null;
  }
  lastThrottleTime = 0;

  // Reset all state and handler references
  rpcRegistered = false;
  currentRoom = null;
  participantBrightness.clear();
  participantsSentBrightness.clear();
  lastSentBrightness = 100;
  handleParticipantDisconnected = null;
  handleRoomDisconnected = null;
  handleConnectionStateChanged = null;
}
